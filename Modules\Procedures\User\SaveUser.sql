-- SaveUser.sql
-- Contains queries for saving user information

-- [InsertUser] --
INSERT INTO users (
    username,
    password_hash,
    password_salt,
    full_name,
    email,
    role,
    is_active
) VALUES (
    @username,
    @password_hash,
    @password_salt,
    @full_name,
    @email,
    @role,
    @is_active
)
RETURNING user_id;
-- [End] --

-- [UpdateUser] --
UPDATE users
SET
    username = @username,
    full_name = @full_name,
    email = @email,
    role = @role,
    is_active = @is_active
WHERE
    user_id = @user_id;
-- [End] --

-- [UpdateUserPassword] --
UPDATE users
SET
    password_hash = @password_hash,
    password_salt = @password_salt
WHERE
    user_id = @user_id;
-- [End] --

-- [UpdateLastLogin] --
UPDATE users
SET
    last_login_date = CURRENT_TIMESTAMP
WHERE
    user_id = @user_id;
-- [End] --
