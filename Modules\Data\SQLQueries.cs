using System;

namespace ProManage.Modules.Data
{
    /// <summary>
    /// Constants class for SQL query file paths.
    /// Organizes query file paths by module for easy reference.
    /// </summary>
    public static class SQLQueries
    {
        /// <summary>
        /// Constants for Estimate module SQL queries
        /// </summary>
        public static class Estimate
        {
            // Module name (subfolder in Modules/Procedures)
            public const string MODULE_NAME = "Estimate";

            // Query file names (updated to match actual SQL files)
            public const string GET_ALL_ESTIMATES = "EstimateListing";
            public const string GET_ESTIMATE_BY_ID = "EstimateRetrieval";
            public const string GET_ESTIMATE_BY_NUMBER = "EstimateRetrieval";
            public const string SEARCH_ESTIMATES_BY_CUSTOMER = "EstimateListing";
            public const string ESTIMATE_CRUD = "EstimateCRUD";
            public const string DELETE_ESTIMATE = "EstimateDelete";
            public const string GET_ESTIMATE_LIST = "EstimateListing";
            public const string GET_NEXT_ESTIMATE_NUMBER = "EstimateUtilities";

            // Navigation query file names
            public const string GET_FIRST_ESTIMATE = "GetFirstEstimate";
            public const string GET_LAST_ESTIMATE = "GetLastEstimate";
            public const string GET_PREVIOUS_ESTIMATE = "GetPreviousEstimate";
            public const string GET_NEXT_ESTIMATE = "GetNextEstimate";

            /// <summary>
            /// Named queries within EstimateCRUD.sql file
            /// </summary>
            public static class EstimateCRUD
            {
                public const string INSERT_HEADER = "InsertHeader";
                public const string UPDATE_HEADER = "UpdateHeader";
                public const string DELETE_ALL_DETAILS = "DeleteAllDetails";
                public const string INSERT_DETAIL = "InsertDetail";
                public const string UPSERT_HEADER = "UpsertHeader";
                public const string UPSERT_DETAIL = "UpsertDetail";
            }

            /// <summary>
            /// Named queries within SaveEstimate.sql file (legacy - use EstimateCRUD instead)
            /// </summary>
            public static class SaveEstimate
            {
                public const string INSERT_HEADER = "InsertHeader";
                public const string UPDATE_HEADER = "UpdateHeader";
                public const string DELETE_DETAILS = "DeleteAllDetails";
                public const string INSERT_DETAIL = "InsertDetail";
                public const string INSERT_HEADER_WITH_ID = "InsertHeaderWithId";
                public const string GET_NEXT_DETAIL_ID = "GetNextDetailId";
            }

            /// <summary>
            /// Named queries within EstimateRetrieval.sql file
            /// </summary>
            public static class EstimateRetrieval
            {
                public const string GET_BY_ID = "GetById";
                public const string GET_BY_NUMBER = "GetByNumber";
                public const string GET_DETAILS_BY_ID = "GetDetailsById";
                public const string GET_DETAILS_BY_NUMBER = "GetDetailsByNumber";
            }

            /// <summary>
            /// Named queries within DeleteEstimate.sql file
            /// </summary>
            public static class DeleteEstimate
            {
                public const string DELETE_ESTIMATE = "DeleteEstimate";
            }

            /// <summary>
            /// Named queries within GetEstimateList.sql file
            /// </summary>
            public static class GetEstimateList
            {
                public const string GET_ESTIMATE_LIST = "GetEstimateList";
                public const string GET_ESTIMATE_COUNT = "GetEstimateCount";
            }

            /// <summary>
            /// Named queries within EstimateNavigation.sql file
            /// </summary>
            public static class EstimateNavigation
            {
                public const string GET_FIRST_ESTIMATE = "GetFirstEstimate";
                public const string GET_LAST_ESTIMATE = "GetLastEstimate";
                public const string GET_PREVIOUS_ESTIMATE = "GetPreviousEstimate";
                public const string GET_NEXT_ESTIMATE = "GetNextEstimate";
                public const string GET_ESTIMATE_DETAILS = "GetEstimateDetails";
                public const string GET_ESTIMATE_COUNT = "GetEstimateCount";
                public const string GET_ESTIMATE_POSITION = "GetEstimatePosition";
            }

            /// <summary>
            /// Named queries within navigation SQL files
            /// </summary>
            public static class Navigation
            {
                public const string GET_HEADER = "GetHeader";
                public const string GET_DETAILS = "GetDetails";
            }
        }

        /// <summary>
        /// Constants for Customer module SQL queries
        /// </summary>
        public static class Customer
        {
            // Module name (subfolder in Modules/Procedures)
            public const string MODULE_NAME = "Customer";

            // Query file names (placeholder - add actual file names when created)
            public const string GET_ALL_CUSTOMERS = "GetAllCustomers";
            public const string GET_CUSTOMER_BY_ID = "GetCustomerById";
            public const string SEARCH_CUSTOMERS = "SearchCustomers";
        }

        /// <summary>
        /// Constants for Product module SQL queries
        /// </summary>
        public static class Product
        {
            // Module name (subfolder in Modules/Procedures)
            public const string MODULE_NAME = "Product";

            // Query file names (placeholder - add actual file names when created)
            public const string GET_ALL_PRODUCTS = "GetAllProducts";
            public const string GET_PRODUCT_BY_ID = "GetProductById";
            public const string SEARCH_PRODUCTS = "SearchProducts";
        }

        /// <summary>
        /// Constants for SQLQuery module SQL queries
        /// </summary>
        public static class SQLQuery
        {
            // Module name (subfolder in Modules/Procedures)
            public const string MODULE_NAME = "SQLQuery";

            // Query file names
            public const string GET_ALL_TABLES = "GetAllTables";
            public const string GET_TABLE_COLUMNS = "GetTableColumns";
            public const string GET_TABLE_DATA = "GetTableData";
            public const string GET_TABLE_CONSTRAINTS = "GetTableConstraints";
            public const string GET_TABLE_INDEXES = "GetTableIndexes";
            public const string GET_TABLE_FOREIGN_KEYS = "GetTableForeignKeys";
            public const string GET_TABLE_PRIMARY_KEY = "GetTablePrimaryKey";
        }

        /// <summary>
        /// Constants for User module SQL queries
        /// </summary>
        public static class User
        {
            // Module name (subfolder in Modules/Procedures)
            public const string MODULE_NAME = "User";

            // Query file names
            public const string GET_USER = "GetUser";
            public const string VALIDATE_USER = "ValidateUser";
            public const string SAVE_USER = "SaveUser";
            public const string USER_MANAGEMENT = "UserManagement";

            /// <summary>
            /// Named queries within GetUser.sql file
            /// </summary>
            public static class GetUser
            {
                public const string GET_USER_BY_USERNAME = "GetUserByUsername";
                public const string GET_USER_BY_ID = "GetUserById";
                public const string GET_ALL_USERS = "GetAllUsers";
                public const string GET_ACTIVE_USERS = "GetActiveUsers";
                public const string SEARCH_USERS = "SearchUsers";
            }

            /// <summary>
            /// Named queries within SaveUser.sql file
            /// </summary>
            public static class SaveUser
            {
                public const string INSERT_USER = "InsertUser";
                public const string UPDATE_USER = "UpdateUser";
                public const string UPDATE_USER_PASSWORD = "UpdateUserPassword";
                public const string UPDATE_LAST_LOGIN = "UpdateLastLogin";
            }

            /// <summary>
            /// Named queries within UserManagement.sql file
            /// </summary>
            public static class UserManagement
            {
                public const string DELETE_USER = "DeleteUser";
                public const string CHECK_USERNAME_EXISTS = "CheckUsernameExists";
                public const string CHECK_EMAIL_EXISTS = "CheckEmailExists";
                public const string GET_USER_COUNT = "GetUserCount";
                public const string GET_ACTIVE_USER_COUNT = "GetActiveUserCount";
                public const string GET_USERS_BY_ROLE = "GetUsersByRole";
                public const string DEACTIVATE_USER = "DeactivateUser";
                public const string ACTIVATE_USER = "ActivateUser";
                public const string GET_DISTINCT_ROLES = "GetDistinctRoles";
            }
        }

        /// <summary>
        /// Constants for System module SQL queries
        /// </summary>
        public static class System
        {
            // Module name (subfolder in Modules/Procedures)
            public const string MODULE_NAME = "System";

            // Query file names
            public const string GET_TABLES = "GetTables";
            public const string GET_TABLE_COLUMNS = "GetTableColumns";
        }
    }
}
