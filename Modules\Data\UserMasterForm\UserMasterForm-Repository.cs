using System;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using Npgsql;
using ProManage.Modules.Data;
using ProManage.Modules.Models.UserMasterForm;
using ProManage.Modules.UI;

namespace ProManage.Modules.Data.UserMasterForm
{
    /// <summary>
    /// Repository class for UserMasterForm database operations
    /// </summary>
    public static class UserMasterFormRepository
    {
        /// <summary>
        /// Gets all users from the database
        /// </summary>
        /// <returns>List of users</returns>
        public static List<UserMasterFormModel> GetAllUsers()
        {
            try
            {
                ProgressIndicatorService.Instance.ShowProgress("Loading users...");

                var query = SQLQueryLoader.LoadNamedQuery(
                    SQLQueries.User.MODULE_NAME,
                    SQLQueries.User.GET_USER,
                    SQLQueries.User.GetUser.GET_ALL_USERS
                );

                var users = new List<UserMasterFormModel>();

                using (var connection = DatabaseConnectionManager.Instance.GetConnection())
                {
                    using (var command = new NpgsqlCommand(query, connection))
                    {
                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                users.Add(MapReaderToModel(reader));
                            }
                        }
                    }
                }

                Debug.WriteLine($"Retrieved {users.Count} users from database");
                return users;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error retrieving users: {ex.Message}");
                throw new Exception($"Failed to retrieve users: {ex.Message}", ex);
            }
            finally
            {
                ProgressIndicatorService.Instance.HideProgress();
            }
        }

        /// <summary>
        /// Gets a user by ID
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>User model or null if not found</returns>
        public static UserMasterFormModel GetUserById(int userId)
        {
            try
            {
                var query = SQLQueryLoader.LoadNamedQuery(
                    SQLQueries.User.MODULE_NAME,
                    SQLQueries.User.GET_USER,
                    SQLQueries.User.GetUser.GET_USER_BY_ID
                );

                using (var connection = DatabaseConnectionManager.Instance.GetConnection())
                {
                    using (var command = new NpgsqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@user_id", userId);

                        using (var reader = command.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                return MapReaderToModel(reader);
                            }
                        }
                    }
                }

                return null;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error retrieving user by ID {userId}: {ex.Message}");
                throw new Exception($"Failed to retrieve user: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Searches users by search term
        /// </summary>
        /// <param name="searchTerm">Search term</param>
        /// <returns>List of matching users</returns>
        public static List<UserMasterFormModel> SearchUsers(string searchTerm)
        {
            try
            {
                ProgressIndicatorService.Instance.ShowProgress("Searching users...");

                var query = SQLQueryLoader.LoadNamedQuery(
                    SQLQueries.User.MODULE_NAME,
                    SQLQueries.User.GET_USER,
                    SQLQueries.User.GetUser.SEARCH_USERS
                );

                var users = new List<UserMasterFormModel>();

                using (var connection = DatabaseConnectionManager.Instance.GetConnection())
                {
                    using (var command = new NpgsqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@search_term", searchTerm ?? string.Empty);

                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                users.Add(MapReaderToModel(reader));
                            }
                        }
                    }
                }

                Debug.WriteLine($"Found {users.Count} users matching '{searchTerm}'");
                return users;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error searching users: {ex.Message}");
                throw new Exception($"Failed to search users: {ex.Message}", ex);
            }
            finally
            {
                ProgressIndicatorService.Instance.HideProgress();
            }
        }

        /// <summary>
        /// Maps database reader to user model
        /// </summary>
        /// <param name="reader">Database reader</param>
        /// <returns>User model</returns>
        private static UserMasterFormModel MapReaderToModel(IDataReader reader)
        {
            return new UserMasterFormModel
            {
                UserId = reader.GetInt32("user_id"),
                Username = reader.IsDBNull("username") ? string.Empty : reader.GetString("username"),
                FullName = reader.IsDBNull("full_name") ? string.Empty : reader.GetString("full_name"),
                Email = reader.IsDBNull("email") ? string.Empty : reader.GetString("email"),
                Role = reader.IsDBNull("role") ? string.Empty : reader.GetString("role"),
                Department = reader.IsDBNull("department") ? string.Empty : reader.GetString("department"),
                Phone = reader.IsDBNull("phone") ? string.Empty : reader.GetString("phone"),
                IsActive = reader.IsDBNull("is_active") ? false : reader.GetBoolean("is_active"),
                LastLoginDate = reader.IsDBNull("last_login_date") ? (DateTime?)null : reader.GetDateTime("last_login_date"),
                CreatedDate = reader.IsDBNull("created_date") ? (DateTime?)null : reader.GetDateTime("created_date"),
                ModifiedDate = reader.IsDBNull("modified_date") ? (DateTime?)null : reader.GetDateTime("modified_date")
            };
        }
    }
}
