﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Diagnostics;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using DevExpress.XtraGrid.Views.Grid;
using ProManage.Modules.Data.UserMasterForm;
using ProManage.Modules.Models.UserMasterForm;

namespace ProManage.Forms.ListForms
{
    public partial class UserManagementListForm : Form
    {
        #region Private Fields

        private List<UserMasterFormModel> _users;
        private UserMasterFormModel _selectedUser;

        #endregion

        #region Constructor

        public UserManagementListForm()
        {
            InitializeComponent();
            InitializeForm();
        }

        #endregion

        #region Form Initialization

        /// <summary>
        /// Initializes the form and loads data
        /// </summary>
        private void InitializeForm()
        {
            try
            {
                // Set form properties
                this.Text = "User Management";
                this.WindowState = FormWindowState.Maximized;

                // Initialize grid
                InitializeGrid();

                // Wire up events
                WireUpEvents();

                // Load data
                LoadUsers();

                Debug.WriteLine("UserManagementListForm initialized successfully");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error initializing UserManagementListForm: {ex.Message}");
                MessageBox.Show($"Error initializing form: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Initializes the grid configuration
        /// </summary>
        private void InitializeGrid()
        {
            try
            {
                // Configure grid view
                gridViewUsers.OptionsView.ShowGroupPanel = false;
                gridViewUsers.OptionsView.ShowAutoFilterRow = true;
                gridViewUsers.OptionsBehavior.ReadOnly = true;
                gridViewUsers.OptionsSelection.EnableAppearanceFocusedCell = false;
                gridViewUsers.OptionsSelection.EnableAppearanceFocusedRow = true;

                // Configure columns
                ConfigureGridColumns();

                Debug.WriteLine("Grid initialized successfully");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error initializing grid: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Configures grid columns
        /// </summary>
        private void ConfigureGridColumns()
        {
            try
            {
                // Clear existing columns
                gridViewUsers.Columns.Clear();

                // Add columns
                var colUserId = gridViewUsers.Columns.AddField("UserId");
                colUserId.Caption = "ID";
                colUserId.Width = 60;
                colUserId.Visible = false; // Hide ID column

                var colUsername = gridViewUsers.Columns.AddField("Username");
                colUsername.Caption = "Username";
                colUsername.Width = 120;
                colUsername.VisibleIndex = 0;

                var colFullName = gridViewUsers.Columns.AddField("FullName");
                colFullName.Caption = "Full Name";
                colFullName.Width = 200;
                colFullName.VisibleIndex = 1;

                var colEmail = gridViewUsers.Columns.AddField("Email");
                colEmail.Caption = "Email";
                colEmail.Width = 200;
                colEmail.VisibleIndex = 2;

                var colRole = gridViewUsers.Columns.AddField("Role");
                colRole.Caption = "Role";
                colRole.Width = 100;
                colRole.VisibleIndex = 3;

                var colDepartment = gridViewUsers.Columns.AddField("Department");
                colDepartment.Caption = "Department";
                colDepartment.Width = 120;
                colDepartment.VisibleIndex = 4;

                var colPhone = gridViewUsers.Columns.AddField("Phone");
                colPhone.Caption = "Phone";
                colPhone.Width = 120;
                colPhone.VisibleIndex = 5;

                var colDesignation = gridViewUsers.Columns.AddField("Designation");
                colDesignation.Caption = "Designation";
                colDesignation.Width = 150;
                colDesignation.VisibleIndex = 6;

                var colIsActive = gridViewUsers.Columns.AddField("IsActive");
                colIsActive.Caption = "Active";
                colIsActive.Width = 80;
                colIsActive.VisibleIndex = 7;

                var colLastLogin = gridViewUsers.Columns.AddField("LastLoginDate");
                colLastLogin.Caption = "Last Login";
                colLastLogin.Width = 120;
                colLastLogin.VisibleIndex = 8;

                var colCreated = gridViewUsers.Columns.AddField("CreatedDate");
                colCreated.Caption = "Created";
                colCreated.Width = 120;
                colCreated.VisibleIndex = 9;

                Debug.WriteLine("Grid columns configured successfully");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error configuring grid columns: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Wires up event handlers
        /// </summary>
        private void WireUpEvents()
        {
            try
            {
                // Ribbon button events
                barButtonItem2.ItemClick += BtnNew_ItemClick;
                BarButtonItemEdit.ItemClick += BtnEdit_ItemClick;
                BarButtonItemDelete.ItemClick += BtnDelete_ItemClick;
                BarButtonItemSave.ItemClick += BtnRefresh_ItemClick;

                // Grid events
                gridViewUsers.FocusedRowChanged += GridViewUsers_FocusedRowChanged;
                gridViewUsers.DoubleClick += GridViewUsers_DoubleClick;

                Debug.WriteLine("Events wired up successfully");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error wiring up events: {ex.Message}");
                throw;
            }
        }

        #endregion

        #region Data Operations

        /// <summary>
        /// Loads users from the database
        /// </summary>
        private void LoadUsers()
        {
            try
            {
                Debug.WriteLine("Loading users...");

                _users = UserMasterFormRepository.GetAllUsers();
                gridUsers.DataSource = _users;

                // Update button states
                UpdateButtonStates();

                Debug.WriteLine($"Loaded {_users?.Count ?? 0} users");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error loading users: {ex.Message}");
                MessageBox.Show($"Error loading users: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Refreshes the user list
        /// </summary>
        private void RefreshUsers()
        {
            try
            {
                LoadUsers();
                Debug.WriteLine("Users refreshed successfully");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error refreshing users: {ex.Message}");
                MessageBox.Show($"Error refreshing users: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region Event Handlers

        /// <summary>
        /// Handles New button click
        /// </summary>
        private void BtnNew_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            try
            {
                Debug.WriteLine("New button clicked");
                OpenUserMasterForm(null); // null for new user
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in New button: {ex.Message}");
                MessageBox.Show($"Error creating new user: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Handles Edit button click
        /// </summary>
        private void BtnEdit_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            try
            {
                Debug.WriteLine("Edit button clicked");

                if (_selectedUser != null)
                {
                    OpenUserMasterForm(_selectedUser);
                }
                else
                {
                    MessageBox.Show("Please select a user to edit.", "No Selection",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in Edit button: {ex.Message}");
                MessageBox.Show($"Error editing user: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Handles Delete button click
        /// </summary>
        private void BtnDelete_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            try
            {
                Debug.WriteLine("Delete button clicked");

                if (_selectedUser != null)
                {
                    var result = MessageBox.Show(
                        $"Are you sure you want to delete user '{_selectedUser.FullName}'?\n\nThis will deactivate the user account.",
                        "Confirm Delete",
                        MessageBoxButtons.YesNo,
                        MessageBoxIcon.Question);

                    if (result == DialogResult.Yes)
                    {
                        bool success = UserMasterFormRepository.DeleteUser(_selectedUser.UserId);
                        if (success)
                        {
                            MessageBox.Show("User deleted successfully.", "Success",
                                MessageBoxButtons.OK, MessageBoxIcon.Information);
                            RefreshUsers();
                        }
                        else
                        {
                            MessageBox.Show("Failed to delete user.", "Error",
                                MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }
                    }
                }
                else
                {
                    MessageBox.Show("Please select a user to delete.", "No Selection",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in Delete button: {ex.Message}");
                MessageBox.Show($"Error deleting user: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Handles Refresh button click
        /// </summary>
        private void BtnRefresh_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            try
            {
                Debug.WriteLine("Refresh button clicked");
                RefreshUsers();
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in Refresh button: {ex.Message}");
                MessageBox.Show($"Error refreshing users: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Handles grid focused row changed
        /// </summary>
        private void GridViewUsers_FocusedRowChanged(object sender, DevExpress.XtraGrid.Views.Base.FocusedRowChangedEventArgs e)
        {
            try
            {
                UpdateSelectedUser();
                UpdateButtonStates();
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in FocusedRowChanged: {ex.Message}");
            }
        }

        /// <summary>
        /// Handles grid double click
        /// </summary>
        private void GridViewUsers_DoubleClick(object sender, EventArgs e)
        {
            try
            {
                Debug.WriteLine("Grid double-clicked");

                if (_selectedUser != null)
                {
                    OpenUserMasterForm(_selectedUser);
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in DoubleClick: {ex.Message}");
                MessageBox.Show($"Error opening user: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region Helper Methods

        /// <summary>
        /// Updates the selected user based on grid selection
        /// </summary>
        private void UpdateSelectedUser()
        {
            try
            {
                int focusedRowHandle = gridViewUsers.FocusedRowHandle;
                if (focusedRowHandle >= 0 && focusedRowHandle < _users?.Count)
                {
                    _selectedUser = _users[focusedRowHandle];
                    Debug.WriteLine($"Selected user: {_selectedUser?.Username}");
                }
                else
                {
                    _selectedUser = null;
                    Debug.WriteLine("No user selected");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error updating selected user: {ex.Message}");
                _selectedUser = null;
            }
        }

        /// <summary>
        /// Updates button states based on selection
        /// </summary>
        private void UpdateButtonStates()
        {
            try
            {
                bool hasSelection = _selectedUser != null;

                BarButtonItemEdit.Enabled = hasSelection;
                BarButtonItemDelete.Enabled = hasSelection;

                Debug.WriteLine($"Button states updated. Has selection: {hasSelection}");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error updating button states: {ex.Message}");
            }
        }

        /// <summary>
        /// Opens the UserMasterForm for creating or editing a user
        /// </summary>
        /// <param name="user">User to edit, or null for new user</param>
        private void OpenUserMasterForm(UserMasterFormModel user)
        {
            try
            {
                using (var userForm = new UserMasterForm())
                {
                    // Set the user for editing (null for new)
                    if (user != null)
                    {
                        userForm.SetUserForEditing(user);
                        Debug.WriteLine($"Opening UserMasterForm for editing user: {user.Username}");
                    }
                    else
                    {
                        userForm.SetNewUserMode();
                        Debug.WriteLine("Opening UserMasterForm for new user");
                    }

                    // Show as dialog
                    var result = userForm.ShowDialog(this);

                    // Refresh the list if user was saved
                    if (result == DialogResult.OK)
                    {
                        RefreshUsers();
                        Debug.WriteLine("User form closed with OK result, refreshing list");
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error opening UserMasterForm: {ex.Message}");
                MessageBox.Show($"Error opening user form: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion
    }
}
